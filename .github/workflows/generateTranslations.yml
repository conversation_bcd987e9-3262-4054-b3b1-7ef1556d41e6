name: Generate static translations

on:
  pull_request:
    types: [opened, synchronize]
    paths: ['src/languages/en.ts']

  workflow_dispatch:
    inputs:
      PULL_REQUEST_URL:
        description: 'The full URL of the E/App pull request'
        required: true
        type: string

jobs:
  generateTranslations:
    runs-on: ubuntu-latest
    if: ${{ github.event_name == 'workflow_dispatch' || !github.event.pull_request.head.repo.fork }}
    steps:
      - name: Get PR details
        id: pr-data
        run: |
          if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
            echo "::notice::🔧 Manual workflow dispatch triggered"
            PR_INFO=$(gh pr view "${{ inputs.PULL_REQUEST_URL }}" --json number,baseRefName,headRefOid)
            {
              echo "PR_NUMBER=$(echo "$PR_INFO" | jq -r '.number')"
              echo "BASE_REF=$(echo "$PR_INFO" | jq -r '.baseRefName')"
              echo "HEAD_SHA=$(echo "$PR_INFO" | jq -r '.headRefOid')"
            } >> "$GITHUB_OUTPUT"
            echo "::notice::✅ Processing PR #$(echo "$PR_INFO" | jq -r '.number')"
          else
            echo "::notice::🤖 Automatic PR trigger activated"
            {
              echo "PR_NUMBER=${{ github.event.pull_request.number }}"
              echo "BASE_REF=${{ github.event.pull_request.base.ref }}"
              echo "HEAD_SHA=${{ github.event.pull_request.head.sha }}"
            } >> "$GITHUB_OUTPUT"
            echo "::notice::✅ Processing PR #${{ github.event.pull_request.number }}"
          fi
        env:
          GITHUB_TOKEN: ${{ github.token }}

      # v4
      - name: Checkout
        uses: actions/checkout@8ade135a41bc03ea155e62e844d188df1ea18608
        with:
          ref: ${{ steps.pr-data.outputs.HEAD_SHA }}

      - name: Setup Node
        uses: ./.github/actions/composite/setupNode

      - name: Check if English translations were modified
        id: check-en-changes
        run: |
          if [[ "${{ github.event.action }}" == "synchronize" ]]; then
            echo "🔄 PR was updated - checking only the new commits for en.ts changes"
            git fetch --no-tags --depth=1 --no-recurse-submodules origin ${{ github.event.before }}
            if git diff --name-only ${{ github.event.before }} ${{ github.event.after }} | grep -q "^src/languages/en\.ts$"; then
              echo "EN_CHANGED=true" >> "$GITHUB_OUTPUT"
              echo "✅ English translations were modified in the new commits - will generate translations"
            else
              echo "EN_CHANGED=false" >> "$GITHUB_OUTPUT"
              echo "⏭️ English translations were not modified in the new commits - skipping translation generation"
            fi
          else
            echo "🆕|🔧 Newly opened PR or manually dispatched, so checking entire PR for en.ts changes"
            if gh pr diff ${{ steps.pr-data.outputs.PR_NUMBER }} --name-only | grep -q "^src/languages/en\.ts$"; then
              echo "EN_CHANGED=true" >> "$GITHUB_OUTPUT"
              echo "✅ English translations were modified in this PR - will generate translations"
            else
              echo "EN_CHANGED=false" >> "$GITHUB_OUTPUT"
              echo "⏭️ English translations were not modified in this PR - skipping translation generation"
            fi
          fi
        env:
          GITHUB_TOKEN: ${{ github.token }}

      - name: Fetch base ref
        if: steps.check-en-changes.outputs.EN_CHANGED == 'true'
        run: git fetch --no-tags --depth=1 --no-recurse-submodules origin +refs/heads/${{ steps.pr-data.outputs.BASE_REF }}:refs/heads/${{ steps.pr-data.outputs.BASE_REF }}

      - name: Run generateTranslations for added translations
        if: steps.check-en-changes.outputs.EN_CHANGED == 'true'
        run: npx ts-node ./scripts/generateTranslations.ts --verbose --compare-ref=${{ steps.pr-data.outputs.BASE_REF }}
        env:
          GITHUB_TOKEN: ${{ github.token }}
          OPENAI_API_KEY: ${{ secrets.PROPOSAL_POLICE_API_KEY }}

      - name: Check if there are any changes after running generateTranslations
        if: steps.check-en-changes.outputs.EN_CHANGED == 'true'
        id: checkDiff
        run: |
          if git diff --quiet HEAD; then
            echo "✅ No changes detected"
            echo "HAS_DIFF=false" >> "$GITHUB_OUTPUT"
          else
            echo "🦜 Polyglot Parrot detected changes! 🦜"
            echo "HAS_DIFF=true" >> "$GITHUB_OUTPUT"
          fi

      - name: Hide any existing Polyglot Parrot comments
        continue-on-error: true
        if: steps.check-en-changes.outputs.EN_CHANGED == 'true' && steps.checkDiff.outputs.HAS_DIFF == 'true'
        run: |
          EXISTING_COMMENTS="$(gh pr view ${{ steps.pr-data.outputs.PR_NUMBER }} --json comments --jq '.comments[] | select(.body | startswith("## 🦜 Polyglot Parrot! 🦜")) | .id')"
          if [[ -z "$EXISTING_COMMENTS" ]]; then
            echo "🦗 No existing Polyglot Parrot comments found"
          else
            echo "Found existing Polyglot Parrot comment(s), hiding as outdated..."
            echo "$EXISTING_COMMENTS" | while read -r comment_id; do
              # shellcheck disable=SC2016
              gh api graphql -f query='
                mutation($commentId: ID!) {
                  minimizeComment(input: {
                    subjectId: $commentId,
                    classifier: OUTDATED
                  }) {
                    minimizedComment {
                      isMinimized
                    }
                  }
                }' -f commentId="$comment_id" || echo "Failed to minimize comment $comment_id"
            done
          fi
        env:
          GITHUB_TOKEN: ${{ github.token }}

      - name: Post diff in PR as a comment
        if: steps.check-en-changes.outputs.EN_CHANGED == 'true' && steps.checkDiff.outputs.HAS_DIFF == 'true'
        run: |
          # Create temp files and set up cleanup
          TEMP_COMMENT_FILE=$(mktemp)
          TEMP_PATCH_FILE=$(mktemp --suffix=.patch)
          trap 'rm -f "$TEMP_COMMENT_FILE" "$TEMP_PATCH_FILE"' EXIT

          # Generate the diff and save to patch file
          git diff HEAD > "$TEMP_PATCH_FILE"

          # Ensure the patch ends with a newline to prevent "corrupt patch" errors
          echo "" >> "$TEMP_PATCH_FILE"

          # Check file size (65536 chars is ~64KB, let's use 60KB as safety margin)
          PATCH_SIZE=$(wc -c < "$TEMP_PATCH_FILE")
          readonly MAX_COMMENT_SIZE=61440  # 60KB in bytes

          readonly PARROT_HEADER="## 🦜 Polyglot Parrot! 🦜"
          readonly PARROT_INTRO="_Squawk!_ Looks like you added some shiny new English strings. Allow me to parrot them back to you in other tongues:"
          PARROT_FOOTER="$(cat <<'EOF'
          > [!NOTE]
          > You can apply these changes to your branch by copying the patch to your clipboard, then running `pbpaste | git apply` 😉
          EOF
          )"
          readonly PARROT_FOOTER

          if [ "$PATCH_SIZE" -le "$MAX_COMMENT_SIZE" ]; then
            # Small diff - include in comment
            {
              echo "$PARROT_HEADER"
              echo
              echo "$PARROT_INTRO"
              echo
              echo '```diff'
              cat "$TEMP_PATCH_FILE"
              echo '```'
              echo
              echo "$PARROT_FOOTER"
            } > "$TEMP_COMMENT_FILE"
          else
            # Large diff - upload as gist and link
            echo "📁 Creating gist for large diff..."
            GIST_URL=$(gh gist create "$TEMP_PATCH_FILE" --desc "🦜 Polyglot Parrot translations for PR #${{ steps.pr-data.outputs.PR_NUMBER }}" --filename "translations.patch")
            {
              echo "$PARROT_HEADER"
              echo
              echo "$PARROT_INTRO"
              echo
              echo "The diff is too large to include in this comment _($((PATCH_SIZE / 1000))KB)_, so I've created a gist for you:"
              echo
              echo "📋 **[View the translation diff here](${GIST_URL})** 📋"
              echo
              echo "$PARROT_FOOTER"
            } > "$TEMP_COMMENT_FILE"
          fi

          # Post comment using the temp file
          gh pr comment ${{ steps.pr-data.outputs.PR_NUMBER }} --body-file "$TEMP_COMMENT_FILE"
        env:
          # Use OS_BOTIFY_TOKEN so that we can create a gist
          GITHUB_TOKEN: ${{ secrets.OS_BOTIFY_TOKEN }}
